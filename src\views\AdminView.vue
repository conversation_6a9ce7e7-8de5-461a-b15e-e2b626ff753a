<template>
  <div class="flex flex-col items-center min-h-screen bg-gray-100 pt-10">
    <h1 class="text-2xl font-bold mb-6">管理员页面</h1>
    <van-button type="primary" @click="showChannel = true" class="mb-6">通道管理</van-button>
    <van-popup v-model:show="showChannel" position="bottom" :style="{ height: '90%' }">
      <div class="p-4 bg-white rounded-t-xl h-full flex flex-col">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">通道管理</h2>
          <van-button icon="cross" size="small" @click="showChannel = false" plain />
        </div>
        <van-form @submit="onAddChannel" class="mb-4 flex flex-wrap gap-2">
          <!-- <van-field
            v-model="newChannel.name"
            label="通道名称"
            placeholder="请输入通道名称"
            required
            class="flex-1 min-w-[160px]"
          /> -->
          <van-field
            v-model="newChannel.channel_no"
            label="通道编号"
            placeholder="请输入通道编号"
            required
            class="flex-1 min-w-[160px]"
            @focus="onFocusField(index)"
          />
          <van-field
            v-model.number="newChannel.max_capacity"
            label="最大容量"
            type="number"
            placeholder="请输入最大容量"
            required
            class="flex-1 min-w-[160px]"
          />
          <van-field
            v-model.number="newChannel.warn_capacity"
            label="警告容量"
            type="number"
            placeholder="请输入警告容量"
            required
            class="flex-1 min-w-[160px]"
          />
          <van-field
            v-model="newChannel.mode_name"
            label="模式"
            required
            class="flex-1 min-w-[160px]"
            readonly
            clickable
            @click="showModePicker = true"
            placeholder="请选择模式"
          />
          <van-popup v-model:show="showModePicker" position="bottom">
            <van-picker
              :columns="modeOptions"
              @confirm="onModeConfirm"
              @cancel="showModePicker = false"
            />
          </van-popup>
          <div class="w-full flex justify-end mt-2">
            <van-button round type="success" native-type="submit">新增通道</van-button>
          </div>
        </van-form>
        <div class="overflow-x-auto flex-1">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
            <thead>
              <tr class="bg-gray-50">
                <!-- <th class="py-2 px-3 text-center">名称</th> -->
                <th class="py-2 px-3 text-center">编号</th>
                <th class="py-2 px-3 text-center">最大容量</th>
                <th class="py-2 px-3 text-center">警告容量</th>
                <th class="py-2 px-3 text-center">模式</th>
                <th class="py-2 px-3 text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="channel in channels" :key="channel.id" class="border-t">
                <!-- <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model="editChannel.name"
                    placeholder="名称"
                    class="w-24"
                  />
                  <span v-else>{{ channel.name }}</span>
                </td> -->
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model="editChannel.channel_no"
                    placeholder="编号"
                    class="w-24"
                  />
                  <span v-else>{{ channel.channel_no }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model.number="editChannel.max_capacity"
                    type="number"
                    placeholder="最大容量"
                    class="w-20"
                  />
                  <span v-else>{{ channel.max_capacity }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <van-field
                    v-if="editId === channel.id"
                    v-model.number="editChannel.warn_capacity"
                    type="number"
                    placeholder="警告容量"
                    class="w-20"
                  />
                  <span v-else>{{ channel.warn_capacity }}</span>
                </td>
                <td class="py-1 px-2 text-center">
                  <template v-if="editId === channel.id">
                    <van-field
                      v-model="editChannel.mode_name"
                      placeholder="模式"
                      class="w-20"
                      readonly
                      clickable
                      @click="showEditModePicker = true"
                    />
                    <van-popup v-model:show="showEditModePicker" position="bottom">
                      <van-picker
                        :columns="modeOptions"
                        @confirm="onEditModeConfirm"
                        @cancel="showEditModePicker = false"
                      />
                    </van-popup>
                  </template>
                  <span v-else>{{
                    modeOptions.find((option) => option.value === channel.mode)?.text
                  }}</span>
                </td>
                <td class="py-1 px-2 text-center flex flex-col gap-1">
                  <div v-if="editId !== channel.id" class="flex gap-1 justify-center">
                    <van-button size="small" type="primary" @click="onEdit(channel)"
                      >编辑</van-button
                    >
                    <van-button size="small" type="danger" @click="onDeleteChannel(channel)"
                      >删除</van-button
                    >
                  </div>
                  <div v-else class="flex gap-1 justify-center">
                    <van-button size="small" type="success" @click="onUpdateChannel"
                      >保存</van-button
                    >
                    <van-button size="small" @click="cancelEdit">取消</van-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </van-popup>
    <!-- 可在此添加更多管理功能 -->
    <van-number-keyboard
      :show="show"
      theme="custom"
      extra-key="."
      z-index="9999"
      close-button-text="完成"
      @blur="onKeyboardBlur"
      @input="onInput"
      @delete="onDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  getChannels,
  addChannel,
  deleteChannel,
  updateChannel,
  channelRes,
  channelReq,
} from '../api/channel'
import { showConfirmDialog, showToast } from 'vant'

const show = ref(false) // 修改：初始为false
const showChannel = ref(false)
const channels = ref<channelRes[]>([])
const newChannel = reactive<Omit<channelReq, 'id'>>({
  channel_no: 'A',
  max_capacity: 0,
  mode: '',
  mode_name: '',
  name: '',
  warn_capacity: 0,
})

const editId = ref<number | null>(null)
const editChannel = reactive<Partial<channelRes>>({})

const fetchChannels = async () => {
  channels.value = await getChannels()
}

const onAddChannel = async () => {
  await addChannel({ ...newChannel })
  Object.assign(newChannel, {
    channel_no: '',
    max_capacity: 0,
    mode: '',
    name: '',
    warn_capacity: 0,
  })
  await fetchChannels()
  showToast({ message: '新增成功' })
}

const onDeleteChannel = async (channel: channelRes) => {
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除通道「${channel.name}」吗？`,
  })
    .then(async () => {
      await deleteChannel(channel)
      await fetchChannels()
      showToast({ message: '删除成功' })
    })
    .catch(() => {})
}

const onEdit = (channel: channelRes) => {
  editId.value = channel.id
  Object.assign(editChannel, channel)
}

const onUpdateChannel = async () => {
  if (editId.value !== null) {
    await updateChannel(editChannel as channelRes)
    editId.value = null
    await fetchChannels()
    showToast({ message: '修改成功' })
  }
}

const cancelEdit = () => {
  editId.value = null
}

const modeOptions = [
  { text: '普通', value: 'Normal' },
  { text: '合并', value: 'Combine' },
  { text: '回吸', value: 'Suck_back' },
]
const showModePicker = ref(false)
const showEditModePicker = ref(false)
const onModeConfirm = ({ selectedValues, selectedOptions }) => {
  newChannel.mode = selectedValues
  newChannel.mode_name = selectedOptions[0].text
  showModePicker.value = false
}

const onEditModeConfirm = ({ selectedValues, selectedOptions }) => {
  console.log(selectedValues)

  editChannel.mode = selectedValues[0]
  editChannel.mode_name = selectedOptions[0].text
  showEditModePicker.value = false
}

const onFocusField = (index: number) => {
  // currentChannelIndex.value = index
  show.value = true
}

onMounted(() => {
  fetchChannels()
})
</script>
