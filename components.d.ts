/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    App: typeof import('./src/components/virtual-keyboard/src/App.vue')['default']
    'App copy': typeof import('./src/components/virtual-keyboard/src/App copy.vue')['default']
    copy: typeof import('./src/components/virtual-keyboard/src/App copy.vue')['default']
    FullTriangle: typeof import('./src/components/virtual-keyboard/src/components/keyboard/fullTriangle.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    KeyboardIndex: typeof import('./src/components/virtual-keyboard/src/components/keyboard/keyboardIndex.vue')['default']
    KeyDel: typeof import('./src/components/virtual-keyboard/src/components/keyboard/keyDel.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
    VanNumberKeyboard: typeof import('vant/es')['NumberKeyboard']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
